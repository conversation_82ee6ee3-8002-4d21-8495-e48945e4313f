package org.demo.demo.controller;

import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.control.ListView;
import javafx.scene.control.PasswordField;
import javafx.scene.control.TextField;
import org.demo.demo.dao.UtilisateurDAO;
import org.demo.demo.entities.Utilisateur;
import org.demo.demo.config.DatabaseUtil;
import org.mindrot.jbcrypt.BCrypt; // <-- Import BCrypt

import java.sql.Connection;
import java.util.List;

public class ManageEmployeesController {

    @FXML
    private TextField usernameField;

    @FXML
    private PasswordField passwordField;

    @FXML
    private Label statusLabel;

    @FXML
    private ListView<String> employeeListView;

    private UtilisateurDAO utilisateurDAO;

    @FXML
    public void initialize() {
        try {
            Connection conn = DatabaseUtil.getConnection();
            utilisateurDAO = new UtilisateurDAO(conn);
            refreshEmployeeList();
        } catch (Exception e) {
            statusLabel.setText("Erreur de connexion à la base de données");
        }
    }

    @FXML
    private void handleAddEmployee() {
        String username = usernameField.getText().trim();
        String password = passwordField.getText();

        if (username.isEmpty() || password.isEmpty()) {
            statusLabel.setText("Veuillez remplir tous les champs !");
            return;
        }

        try {
            // Hachage BCrypt du mot de passe
            String hashedPassword = BCrypt.hashpw(password, BCrypt.gensalt());

            // Ajout de l'utilisateur
            Utilisateur user = new Utilisateur(0, username, hashedPassword, "user");
            boolean success = utilisateurDAO.addUtilisateur(user);

            if(success) {
                statusLabel.setText("Employé ajouté avec succès !");
                usernameField.clear();
                passwordField.clear();
                refreshEmployeeList();
            } else {
                statusLabel.setText("Erreur lors de l'ajout de l'employé !");
            }

        } catch (Exception e) {
            e.printStackTrace();
            statusLabel.setText("Erreur lors de l'ajout de l'employé !");
        }
    }

    private void refreshEmployeeList() {
        try {
            List<Utilisateur> users = utilisateurDAO.getAllUtilisateurs();
            employeeListView.getItems().clear();
            for (Utilisateur u : users) {
                employeeListView.getItems().add(u.getUsername());
            }
        } catch (Exception e) {
            statusLabel.setText("Erreur lors du chargement de la liste !");
        }
    }
}
