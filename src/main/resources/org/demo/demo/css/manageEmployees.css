/* manageEmployees.css */

/* Fond général de la page */
.root {
    -fx-background-color: #f4f4f4;
    -fx-font-family: "Arial", sans-serif;
}

/* Conteneur principal */
#mainContainer {
    -fx-padding: 20;
    -fx-spacing: 15;
}

/* Titres et en-têtes */
.label.title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #333333;
}

/* Tableau des employés */
.table-view {
    -fx-background-color: white;
    -fx-border-color: #cccccc;
    -fx-border-width: 1;
    -fx-padding: 10;
}

.table-view .column-header-background {
    -fx-background-color: #007ACC;
}

.table-view .column-header, .table-view .cell {
    -fx-alignment: CENTER_LEFT;
    -fx-padding: 8 10 8 10;
}

/* Cellules */
.table-view .cell {
    -fx-text-fill: #333333;
}

/* Boutons */
.button {
    -fx-background-color: #007ACC;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 8 15 8 15;
    -fx-background-radius: 5;
    -fx-cursor: hand;
    -fx-border-color: transparent;
}

.button:hover {
    -fx-background-color: #005A99;
}

/* Boutons de suppression / actions spécifiques */
.button.delete {
    -fx-background-color: #E74C3C;
}

.button.delete:hover {
    -fx-background-color: #C0392B;
}

/* Champs de saisie */
.text-field, .combo-box {
    -fx-background-radius: 5;
    -fx-border-radius: 5;
    -fx-border-color: #cccccc;
    -fx-padding: 5 10 5 10;
}

/* Message ou notification */
.label.message {
    -fx-text-fill: #E74C3C; /* Rouge pour erreurs */
    -fx-font-weight: bold;
}
