/* Style pour la barre de navigation avec dégradé inspiré du logo Capgemini */
.navbar {
    -fx-background-color: linear-gradient(from 0% 0% to 100% 0%, #4A90E2, #7BB3F0);
    -fx-padding: 15px 20px;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 5, 0, 0, 2);
}

/* Style pour les boutons de la navbar */
.navbar-button {
    -fx-background-color: transparent;
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-padding: 8px 16px;
    -fx-min-width: 100px;
    -fx-cursor: hand;
    -fx-background-radius: 20;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-font-size: 14px;
}

.navbar-button:hover {
    -fx-background-color: rgba(255, 255, 255, 0.15);
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
}

.navbar-button:pressed {
    -fx-background-color: rgba(255, 255, 255, 0.25);
}

.selected-button {
    -fx-background-color: rgba(255, 255, 255, 0.35);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-scale-x: 1.08;
    -fx-scale-y: 1.08;
}


.app-name-label {
    -fx-text-fill: white;
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-padding: 0 15 0 0;
}

/* Style pour le bouton de déconnexion */
.logout-button {
    -fx-background-color: rgba(220, 53, 69, 0.8); /* Rouge légèrement transparent */
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-padding: 8px 16px;
    -fx-min-width: 100px;
    -fx-cursor: hand;
    -fx-background-radius: 20;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-font-size: 14px;
    -fx-border-color: rgba(255, 255, 255, 0.3);
    -fx-border-width: 1px;
    -fx-border-radius: 20;
}

.logout-button:hover {
    -fx-background-color: rgba(220, 53, 69, 1.0); /* Rouge plein au survol */
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 8, 0, 0, 2);
}

.logout-button:pressed {
    -fx-background-color: rgba(180, 43, 59, 1.0); /* Rouge plus foncé quand pressé */
    -fx-scale-y: 0.98;
    -fx-scale-x: 0.98;
}

