<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.shape.*?>
<?import javafx.geometry.Insets?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<BorderPane xmlns:fx="http://javafx.com/fxml"
            fx:controller="org.demo.demo.controller.ManageEmployeesController"
            stylesheets="@css/manageEmployees.css">

    <!-- Navbar -->
    <top>
        <fx:include source="navbar.fxml"/>
    </top>

    <!-- Contenu principal -->
    <center>
        <ScrollPane fitToWidth="true" fitToHeight="true" styleClass="scroll-pane">
            <VBox spacing="30" alignment="CENTER" styleClass="content-section">
                <padding>
                    <Insets top="40" right="40" bottom="40" left="40"/>
                </padding>

                <!-- Titre de la section -->
                <VBox alignment="CENTER" spacing="15">
                    <Label text="Gestion des Employés" styleClass="section-title"/>
                    <Rectangle width="80" height="3" styleClass="section-underline"/>
                </VBox>

                <!-- Formulaire Ajouter Employé -->
                <VBox spacing="20" alignment="CENTER" styleClass="add-employee-section">
                    <Label text="Ajouter un nouvel employé" styleClass="subsection-title"/>

                    <HBox spacing="15" alignment="CENTER">
                        <Label text="Username:"/>
                        <TextField fx:id="usernameField" promptText="Nom d'utilisateur"/>
                    </HBox>

                    <HBox spacing="15" alignment="CENTER">
                        <Label text="Password:"/>
                        <PasswordField fx:id="passwordField" promptText="Mot de passe"/>
                    </HBox>

                    <Button fx:id="addEmployeeButton" text="Ajouter" onAction="#handleAddEmployee" styleClass="primary-button"/>
                    <Label fx:id="statusLabel" text="" styleClass="status-message"/>
                </VBox>

                <!-- Liste des employés existants -->
                <VBox spacing="10" alignment="CENTER" styleClass="employee-list-section">
                    <Label text="Liste des employés" styleClass="subsection-title"/>
                    <ListView fx:id="employeeListView" prefHeight="300"/>
                </VBox>

            </VBox>
        </ScrollPane>
    </center>

</BorderPane>
